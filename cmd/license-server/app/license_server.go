/*
Copyright 2025 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package app

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/julienschmidt/httprouter"
	"github.com/spf13/cobra"
	cliflag "k8s.io/component-base/cli/flag"
	"k8s.io/component-base/cli/globalflag"
	"k8s.io/component-base/logs"
	"k8s.io/component-base/term"
	"k8s.io/component-base/version/verflag"
	"k8s.io/klog/v2"

	"github.com/dynamia-ai/license-server/cmd/license-server/options"
	"github.com/dynamia-ai/license-server/pkg/license/routes"
	"github.com/dynamia-ai/license-server/pkg/license/storage"
)

const (
	// ServerShutdownTimeout defines the maximum time to wait for server shutdown.
	ServerShutdownTimeout = 30 * time.Second
	// APIVersion defines the current API version.
	APIVersion = "v1"
)

// NewLicenseServerCommand creates a *cobra.Command object with default parameters.
// It will run the license server and listen on the specified port.
func NewLicenseServerCommand(ctx context.Context) *cobra.Command {
	opts := options.NewOptions()

	cmd := &cobra.Command{
		Use:   "license-server",
		Short: "Start the license server",
		Long:  `The license server provides license generation and validation services.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			verflag.PrintAndExitIfRequested()
			cliflag.PrintFlags(cmd.Flags())

			if errs := opts.Validate(); len(errs) > 0 {
				for _, err := range errs {
					klog.ErrorS(err, "Configuration validation failed")
				}
				return fmt.Errorf("invalid configuration: %d validation errors", len(errs))
			}

			return Run(ctx, opts)
		},
		Args: cobra.NoArgs, // Simplified: no arguments allowed.
	}

	setupCommandFlags(cmd, opts)
	return cmd
}

// setupCommandFlags configures all command-line flags for the license server.
func setupCommandFlags(cmd *cobra.Command, opts *options.Options) {
	namedFlagSets := opts.Flags()

	verflag.AddFlags(namedFlagSets.FlagSet("global"))
	globalflag.AddGlobalFlags(namedFlagSets.FlagSet("global"), cmd.Name(), logs.SkipLoggingConfigurationFlags())

	fs := cmd.Flags()
	for _, f := range namedFlagSets.FlagSets {
		fs.AddFlagSet(f)
	}

	cols, _, _ := term.TerminalSize(cmd.OutOrStdout())
	cliflag.SetUsageAndHelpFunc(cmd, namedFlagSets, cols)
}

// Run runs the license server with graceful shutdown support.
func Run(ctx context.Context, opts *options.Options) error {
	klog.InfoS("Starting License Server...")
	klog.InfoS("Runtime environment", "GOGC", os.Getenv("GOGC"), "GOMAXPROCS", os.Getenv("GOMAXPROCS"),
		"GOTRACEBACK", os.Getenv("GOTRACEBACK"))

	var st *storage.Storage
	var err error
	if opts.PostgresDSN != "" {
		st, err = storage.NewStorage("postgres", opts.PostgresDSN)
		if err != nil {
			return fmt.Errorf("failed to create storage: %w", err)
		}
	} else if opts.MysqlDSN != "" {
		st, err = storage.NewStorage("mysql", opts.MysqlDSN)
		if err != nil {
			return fmt.Errorf("failed to create storage: %w", err)
		}
	}

	licenseRouter, err := routes.NewLicenseRouter(opts.EncryptKeyPath, opts.DecryptKeyPath, st)
	if err != nil {
		return fmt.Errorf("failed to create license router: %w", err)
	}

	httpRouter := setupHTTPRouter(licenseRouter)
	server := createHTTPServer(opts, httpRouter)

	serverErrChan := make(chan error, 1)
	go func() {
		serverErrChan <- startServer(server, opts)
	}()

	select {
	case err := <-serverErrChan:
		if err != nil && err != http.ErrServerClosed {
			return fmt.Errorf("server error: %w", err)
		}
	case <-ctx.Done():
		// Create shutdown context with timeout.
		shutdownCtx, cancel := context.WithTimeout(context.Background(), ServerShutdownTimeout)
		defer cancel()

		// Gracefully shutdown the server.
		if err := server.Shutdown(shutdownCtx); err != nil {
			klog.ErrorS(err, "Failed to gracefully shutdown server")
			return fmt.Errorf("server shutdown error: %w", err)
		}
		klog.InfoS("Server shutdown completed")
	}

	return nil
}

// setupHTTPRouter configures the HTTP router with all license server routes.
func setupHTTPRouter(licenseRouter *routes.LicenseRouter) *httprouter.Router {
	router := httprouter.New()

	// Add middleware for better error handling.
	router.PanicHandler = func(w http.ResponseWriter, r *http.Request, v any) {
		klog.ErrorS(fmt.Errorf("panic: %v", v), "HTTP handler panic", "method", r.Method, "path", r.URL.Path)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
	}

	apiPrefix := fmt.Sprintf("/api/dynamia.ai/%s", APIVersion)
	router.POST(apiPrefix+"/licenses", licenseRouter.GenerateLicense())
	router.POST(apiPrefix+"/licenses:decode", licenseRouter.DecodeLicense())
	router.GET(apiPrefix+"/licenses", licenseRouter.ListLicenses())

	router.GET(apiPrefix+"/health", healthCheckHandler())
	router.GET(apiPrefix+"/healthz", healthCheckHandler()) // Kubernetes-style health check.

	klog.InfoS("HTTP routes configured", "apiPrefix", apiPrefix)
	return router
}

// createHTTPServer creates and configures the HTTP server.
func createHTTPServer(opts *options.Options, handler http.Handler) *http.Server {
	var addr string

	if opts.HasTlsCert() {
		addr = fmt.Sprintf("%s:%d", opts.ServerRunOptions.BindAddress, opts.ServerRunOptions.SecurePort)
	} else {
		addr = fmt.Sprintf("%s:%d", opts.ServerRunOptions.BindAddress, opts.ServerRunOptions.InsecurePort)
	}

	return &http.Server{
		Addr:         addr,
		Handler:      handler,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}
}

// startServer starts the HTTP server with appropriate protocol (HTTP/HTTPS).
func startServer(server *http.Server, opts *options.Options) error {
	if opts.HasTlsCert() {
		klog.InfoS("Starting HTTPS server", "address", server.Addr, "certFile", opts.ServerRunOptions.TLSCertFile)
		return server.ListenAndServeTLS(opts.ServerRunOptions.TLSCertFile, opts.ServerRunOptions.TLSPrivateKey)
	} else {
		klog.InfoS("Starting HTTP server", "address", server.Addr)
		return server.ListenAndServe()
	}
}

// healthCheckHandler returns a simple health check handler.
func healthCheckHandler() httprouter.Handle {
	return func(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)

		health := struct {
			Status    string `json:"status"`
			Timestamp string `json:"timestamp"`
			Service   string `json:"service"`
			Version   string `json:"version"`
		}{
			Status:    "healthy",
			Timestamp: time.Now().UTC().Format(time.RFC3339),
			Service:   "license-server",
			Version:   APIVersion,
		}

		if err := json.NewEncoder(w).Encode(health); err != nil {
			klog.ErrorS(err, "Failed to write health check response")
		}
	}
}
