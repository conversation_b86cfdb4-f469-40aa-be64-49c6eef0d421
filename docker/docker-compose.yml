services:
  license-server:
    image: ghcr.io/dynamia-ai/license-server:latest
    container_name: license-server
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - license_logs:/var/log/license-server/
    ports:
      - "8000:8000"
    networks:
      - license-network
    command: [
      "/bin/license-server",
      "--insecure-port=8000",
      "--postgres-dsn=**********************************************/dynamia?sslmode=disable",
      "--v=2"
    ]

  license-server-ui:
    image: ghcr.io/dynamia-ai/license-manager-frontend:latest
    container_name: license-server-ui
    restart: unless-stopped
    depends_on:
      - license-server
    environment:
      API_BACKEND_URL: http://license-server:8000
      APP_NAME: "License Manager"
      VERSION: "1.0.0"
    ports:
      - "3000:80"
    networks:
      - license-network

  postgres:
    image: postgres:15-alpine
    container_name: postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: dangerous0
      POSTGRES_DB: dynamia
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d dynamia"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - license-network

volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./postgresql/data
  license_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs 

networks:
  license-network:
    driver: bridge
