/*
Copyright 2025 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package license

import (
	"testing"
	"time"

	"gotest.tools/v3/assert"
)

// TestManager_EncodeAndDecode tests the complete encode/decode cycle
func TestManager_EncodeAndDecode(t *testing.T) {
	// Create a manager with default keys
	manager, err := NewLicenseManager("", "")
	assert.NilError(t, err)
	assert.Assert(t, manager != nil)

	// Create a test license
	testLicense := &License{
		Id:               "test-license-123",
		ExpireTime:       time.Now().Add(24 * time.Hour),
		CreateTime:       time.Now(),
		ReminderDuration: 7,
		Customer:         "Test Customer",
		Partner:          "Test Partner",
		Phase:            "production",
		Type:             LicenseTypeByID,
		ESN:              "ESN123456",
		DeviceIds:        []string{"device-1", "device-2", "device-3"},
		DeviceModels: []*Model{
			{DeviceType: "GPU", Count: 2},
			{DeviceType: "CPU", Count: 4},
		},
		Comment: "Test license for unit testing",
	}

	// Test encoding
	encodedData, err := manager.Encode(testLicense)
	assert.NilError(t, err)
	assert.Assert(t, len(encodedData) > 0)

	// Test decoding
	decodedLicense, err := manager.Decode(encodedData)
	assert.NilError(t, err)
	assert.Assert(t, decodedLicense != nil)

	// Verify the decoded license matches the original
	assert.Equal(t, decodedLicense.Id, testLicense.Id)
	assert.Equal(t, decodedLicense.Customer, testLicense.Customer)
	assert.Equal(t, decodedLicense.Partner, testLicense.Partner)
	assert.Equal(t, decodedLicense.Phase, testLicense.Phase)
	assert.Equal(t, decodedLicense.Type, testLicense.Type)
	assert.Equal(t, decodedLicense.ESN, testLicense.ESN)
	assert.Equal(t, decodedLicense.Comment, testLicense.Comment)
	assert.Equal(t, decodedLicense.ReminderDuration, testLicense.ReminderDuration)

	// Verify device IDs
	assert.Equal(t, len(decodedLicense.DeviceIds), len(testLicense.DeviceIds))
	for i, deviceId := range testLicense.DeviceIds {
		assert.Equal(t, decodedLicense.DeviceIds[i], deviceId)
	}

	// Verify device models
	assert.Equal(t, len(decodedLicense.DeviceModels), len(testLicense.DeviceModels))
	for i, model := range testLicense.DeviceModels {
		assert.Equal(t, decodedLicense.DeviceModels[i].DeviceType, model.DeviceType)
		assert.Equal(t, decodedLicense.DeviceModels[i].Count, model.Count)
	}

	// Verify times (with some tolerance for encoding/decoding)
	assert.Assert(t, decodedLicense.ExpireTime.Unix() == testLicense.ExpireTime.Unix())
	assert.Assert(t, decodedLicense.CreateTime.Unix() == testLicense.CreateTime.Unix())

	// Verify digest was set and is not empty
	assert.Assert(t, decodedLicense.Digest != "")
}

// TestManager_LargeDataEncoding tests encoding/decoding with large amounts of data
func TestManager_LargeDataEncoding(t *testing.T) {
	manager, err := NewLicenseManager("", "")
	assert.NilError(t, err)

	// Create a license with large amounts of data
	var largeDeviceIds []string
	for i := 0; i < 100; i++ {
		largeDeviceIds = append(largeDeviceIds, "device-"+string(rune('a'+i%26))+"-"+string(rune('0'+i%10)))
	}

	var largeDeviceModels []*Model
	for i := 0; i < 50; i++ {
		largeDeviceModels = append(largeDeviceModels, &Model{
			DeviceType: "GPU-Type-" + string(rune('A'+i%26)),
			Count:      i + 1,
		})
	}

	largeLicense := &License{
		Id:           "large-data-test",
		ExpireTime:   time.Now().Add(24 * time.Hour),
		CreateTime:   time.Now(),
		Customer:     "Large Data Customer with very long name that exceeds normal limits",
		Partner:      "Large Data Partner with extensive description and details",
		Phase:        "production-with-extended-phase-information",
		Type:         LicenseTypeByModel,
		ESN:          "ESN-LARGE-DATA-123456789",
		DeviceIds:    largeDeviceIds,
		DeviceModels: largeDeviceModels,
		Comment:      "This is a test license with large amounts of data to verify that the encoding and decoding process can handle substantial payloads without issues.",
	}

	encodedData, err := manager.Encode(largeLicense)
	assert.NilError(t, err)
	assert.Assert(t, len(encodedData) > 0)

	decodedLicense, err := manager.Decode(encodedData)
	assert.NilError(t, err)
	assert.Assert(t, decodedLicense != nil)

	// Verify all data is preserved
	assert.Equal(t, decodedLicense.Id, largeLicense.Id)
	assert.Equal(t, decodedLicense.Customer, largeLicense.Customer)
	assert.Equal(t, decodedLicense.Comment, largeLicense.Comment)
	assert.Equal(t, len(decodedLicense.DeviceIds), len(largeLicense.DeviceIds))
	assert.Equal(t, len(decodedLicense.DeviceModels), len(largeLicense.DeviceModels))

	// Spot check some device IDs and models
	assert.Equal(t, decodedLicense.DeviceIds[0], largeLicense.DeviceIds[0])
	assert.Equal(t, decodedLicense.DeviceIds[99], largeLicense.DeviceIds[99])
	assert.Equal(t, decodedLicense.DeviceModels[0].DeviceType, largeLicense.DeviceModels[0].DeviceType)
	assert.Equal(t, decodedLicense.DeviceModels[49].Count, largeLicense.DeviceModels[49].Count)
}

// TestManager_DigestVerification tests that digest verification works correctly
func TestManager_DigestVerification(t *testing.T) {
	encodeManager, err := NewEncodeOnlyManager("")
	assert.NilError(t, err)

	decodeManager, err := NewDecodeOnlyManager("")
	assert.NilError(t, err)

	testLicense := &License{
		Id:         "digest-test-789",
		ExpireTime: time.Now().Add(24 * time.Hour),
		CreateTime: time.Now(),
		Customer:   "Digest Test Customer",
		Type:       LicenseTypeByID,
		ESN:        "ESN-DIGEST-123",
		DeviceIds:  []string{"device-1"},
	}

	// Encode the license
	encodedData, err := encodeManager.Encode(testLicense)
	assert.NilError(t, err)

	// Decode should work fine with correct data
	decodedLicense, err := decodeManager.Decode(encodedData)
	assert.NilError(t, err)
	assert.Assert(t, decodedLicense.Digest != "")

	// Verify that tampering with encoded data causes digest verification to fail
	// We'll modify a byte in the middle of the encoded data
	if len(encodedData) > 10 {
		tamperedData := make([]byte, len(encodedData))
		copy(tamperedData, encodedData)

		// Change a character in the middle (but keep it valid base64)
		midIndex := len(tamperedData) / 2
		if tamperedData[midIndex] == 'A' {
			tamperedData[midIndex] = 'B'
		} else {
			tamperedData[midIndex] = 'A'
		}

		// This should fail during decryption or digest verification
		_, err = decodeManager.Decode(tamperedData)
		assert.Assert(t, err != nil) // Should fail
	}
}

// BenchmarkManager_EncodeDecodeRoundTrip benchmarks the complete round trip
func BenchmarkManager_EncodeDecodeRoundTrip(b *testing.B) {
	manager, err := NewLicenseManager("", "")
	if err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Create a fresh license for each iteration to avoid digest conflicts
		testLicense := &License{
			Id:         "benchmark-roundtrip-test",
			ExpireTime: time.Now().Add(24 * time.Hour),
			CreateTime: time.Now(),
			Customer:   "Benchmark Roundtrip Customer",
			Type:       LicenseTypeByID,
			ESN:        "ESN-ROUNDTRIP-789",
			DeviceIds:  []string{"device-a", "device-b"},
		}

		encodedData, err := manager.Encode(testLicense)
		if err != nil {
			b.Fatal(err)
		}

		_, err = manager.Decode(encodedData)
		if err != nil {
			b.Fatal(err)
		}
	}
}
