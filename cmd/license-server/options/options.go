/*
Copyright 2025 The HAMi Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package options

import (
	"flag"
	"strings"

	"github.com/spf13/pflag"
	cliflag "k8s.io/component-base/cli/flag"
	"k8s.io/klog/v2"
)

type ServerRunOptions struct {
	// server bind address.
	BindAddress string
	// insecure port number.
	InsecurePort int
	// secure port number.
	SecurePort int
	// tls cert file.
	TLSCertFile string
	// tls private key file.
	TLSPrivateKey string
}

func NewServerRunOptions() *ServerRunOptions {
	// create default server run options.
	return &ServerRunOptions{
		BindAddress:   "0.0.0.0",
		InsecurePort:  8000,
		SecurePort:    0,
		TLSCertFile:   "",
		TLSPrivateKey: "",
	}
}

func (s *ServerRunOptions) AddFlags(fs *pflag.FlagSet, c *ServerRunOptions) {
	fs.StringVar(&s.BindAddress, "bind-address", c.BindAddress, "server bind address")
	fs.IntVar(&s.InsecurePort, "insecure-port", c.InsecurePort, "insecure port number")
	fs.IntVar(&s.SecurePort, "secure-port", s.SecurePort, "secure port number")
	fs.StringVar(&s.TLSCertFile, "tls-cert-file", c.TLSCertFile, "tls cert file")
	fs.StringVar(&s.TLSPrivateKey, "tls-private-key", c.TLSPrivateKey, "tls private key")
}

type Options struct {
	ServerRunOptions *ServerRunOptions
	EncryptKeyPath   string
	DecryptKeyPath   string
	PostgresDSN      string
	MysqlDSN         string
}

func NewOptions() *Options {
	return &Options{
		ServerRunOptions: NewServerRunOptions(),
	}
}

func (o *Options) Flags() cliflag.NamedFlagSets {
	fss := cliflag.NamedFlagSets{}
	fs := fss.FlagSet("generic")
	o.ServerRunOptions.AddFlags(fs, o.ServerRunOptions)

	// fs.StringVar(&o.EncryptKeyPath, "encrypt-key-path", "", "encrypt key path")
	// fs.StringVar(&o.DecryptKeyPath, "decrypt-key-path", "", "decrypt key path")
	fs.StringVar(&o.PostgresDSN, "postgres-dsn", "", "postgres dsn to connect to postgresql")
	fs.StringVar(&o.MysqlDSN, "mysql-dsn", "", "mysql dsn to connect to postgresql")

	fs = fss.FlagSet("klog")
	local := flag.NewFlagSet("klog", flag.ExitOnError)
	klog.InitFlags(local)
	local.VisitAll(func(fl *flag.Flag) {
		fl.Name = strings.ReplaceAll(fl.Name, "_", "-")
		fs.AddGoFlag(fl)
	})

	return fss
}

func (o *Options) HasTlsCert() bool {
	return len(o.ServerRunOptions.TLSCertFile) > 0 && len(o.ServerRunOptions.TLSPrivateKey) > 0
}
