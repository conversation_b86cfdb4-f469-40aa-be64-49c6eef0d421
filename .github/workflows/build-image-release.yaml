name: Build & Release Image

env:
  # ghcr.io/dynamia-ai/license-server/license-server => ghcr.io/dynamia-ai/license-server
  # IMAGE_REPO: ${{ github.repository }}
  IMAGE_REPO: dynamia-ai
  REGISTER: ghcr.io
  IMAGE_ROOT_PATH: docker
  # BUILD_PLATFORM: linux/amd64,linux/arm64
  BUILD_PLATFORM: linux/amd64
  REGISTER_USER: ${{ github.actor }}
  REGISTER_PASSWORD: ${{ secrets.GITHUB_TOKEN }}

on:
  workflow_dispatch:
  push:
    branches:
      - main
    tags:
      - v*

jobs:
  docker-build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@master
      - name: Get the version
        id: get_version
        run: |
          VERSION=${GITHUB_REF#refs/tags/}
          if [[ ${GITHUB_REF} == "refs/heads/main" ]]; then
            VERSION=latest
          fi
          echo ::set-output name=VERSION::${VERSION}

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Login registry
        run: |
          echo "${{ env.REGISTER_PASSWORD }}" | docker login ${{ env.REGISTER }} -u ${{ env.REGISTER_USER }} --password-stdin

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: image=moby/buildkit:master

      - name: Build & Pushing license server image
        uses: docker/build-push-action@v6.13.0
        with:
          context: .
          file: ${{ env.IMAGE_ROOT_PATH }}/Dockerfile
          labels: |-
            org.opencontainers.image.source=https://github.com/${{ env.IMAGE_REPO }}
            org.opencontainers.image.revision=${{ github.sha }}
          platforms: ${{ env.BUILD_PLATFORM }}
          build-args: |
            VERSION=${{ steps.get_version.outputs.VERSION }}
          tags: ${{ env.REGISTER }}/${{ env.IMAGE_REPO }}/license-server:${{ steps.get_version.outputs.VERSION }}
          push: true
          provenance: false
          github-token: ${{ env.REGISTER_PASSWORD }}
