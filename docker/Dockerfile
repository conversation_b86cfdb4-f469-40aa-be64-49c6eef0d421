# Build the manager binary
FROM golang:1.24.4 AS builder

<PERSON>N<PERSON> CGO_ENABLED 0
ENV GOOS linux

WORKDIR /workspace
# Copy the Go Modules manifests
COPY go.mod go.mod
COPY go.sum go.sum

# cache deps before building and copying source so that we don't need to re-download as much
# and so that source changes don't invalidate our downloaded layer
RUN go mod download

# Copy the go source
COPY cmd/ cmd/
COPY pkg/ pkg/

# Build
RUN go build -ldflags="-s -w" -a -o license-server cmd/license-server/main.go && echo "Building GOARCH of $GOARCH.."

FROM alpine:3.18
WORKDIR /
COPY --from=builder /workspace/license-server /bin/license-server
